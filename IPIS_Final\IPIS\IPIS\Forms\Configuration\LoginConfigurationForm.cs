using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using IPIS.Models;
using IPIS.Services;
using IPIS.Repositories;
using IPIS.Utils;

namespace IPIS.Forms.Configuration
{
    public partial class LoginConfigurationForm : Form
    {
        private readonly LoginConfigurationService configService;
        private readonly ToastNotification toast;
        private LoginConfiguration currentConfig;

        // UI Controls
        private Panel mainPanel;
        private GroupBox stationInfoGroup;
        private GroupBox appearanceGroup;
        private GroupBox imagesGroup;
        private GroupBox previewGroup;

        private Label lblStationName;
        private TextBox txtStationName;
        private Label lblWelcomeMessage;
        private TextBox txtWelcomeMessage;
        private Label lblSubtitle;
        private TextBox txtSubtitle;

        private Label lblPrimaryColor;
        private Button btnPrimaryColor;
        private Label lblStationTextColor;
        private Button btnStationTextColor;

        private CheckBox chkUseCustomLogo;
        private Label lblLogoPath;
        private TextBox txtLogoPath;
        private Button btnBrowseLogo;
        private CheckBox chkUseBackgroundImage;
        private Label lblBackgroundImagePath;
        private TextBox txtBackgroundImagePath;
        private Button btnBrowseBackground;

        private Panel previewPanel;
        private Button btnSave;
        private Button btnCancel;
        private Button btnReset;

        private ColorDialog colorDialog;
        private OpenFileDialog openFileDialog;
        private ToolTip toolTip;

        public LoginConfigurationForm()
        {
            configService = new LoginConfigurationService(new SQLiteLoginConfigurationRepository());
            toast = new ToastNotification(this);
            currentConfig = configService.GetLoginConfiguration();

            InitializeComponent();
            LoadConfiguration();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Size = new Size(1020, 450);
            this.Text = "Login Configuration";
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 244, 248);

            CreateControls();
            LayoutControls();
            SetupEventHandlers();
        }

        private void CreateControls()
        {
            // Main panel
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Group boxes
            stationInfoGroup = new GroupBox
            {
                Text = "Station Information",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            appearanceGroup = new GroupBox
            {
                Text = "Colors & Appearance",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            imagesGroup = new GroupBox
            {
                Text = "Images & Branding",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            previewGroup = new GroupBox
            {
                Text = "Preview",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            // Station Information controls
            lblStationName = new Label { Text = "Station Name:", AutoSize = true };
            txtStationName = new TextBox { Size = new Size(200, 25) };

            lblWelcomeMessage = new Label { Text = "Welcome Message:", AutoSize = true };
            txtWelcomeMessage = new TextBox { Size = new Size(200, 25) };

            lblSubtitle = new Label { Text = "Subtitle:", AutoSize = true };
            txtSubtitle = new TextBox { Size = new Size(200, 50), Multiline = true };

            // Color controls - improved styling
            lblPrimaryColor = new Label
            {
                Text = "Background Color:",
                AutoSize = true,
                Font = new Font("Segoe UI", 9, FontStyle.Regular)
            };
            btnPrimaryColor = new Button
            {
                Size = new Size(80, 30),
                FlatStyle = FlatStyle.Flat,
                Text = "",
                Cursor = Cursors.Hand
            };
            btnPrimaryColor.FlatAppearance.BorderSize = 1;
            btnPrimaryColor.FlatAppearance.BorderColor = Color.FromArgb(206, 212, 218);

            lblStationTextColor = new Label
            {
                Text = "Text Color:",
                AutoSize = true,
                Font = new Font("Segoe UI", 9, FontStyle.Regular)
            };
            btnStationTextColor = new Button
            {
                Size = new Size(80, 30),
                FlatStyle = FlatStyle.Flat,
                Text = "",
                Cursor = Cursors.Hand
            };
            btnStationTextColor.FlatAppearance.BorderSize = 1;
            btnStationTextColor.FlatAppearance.BorderColor = Color.FromArgb(206, 212, 218);

            // Image controls
            chkUseCustomLogo = new CheckBox { Text = "Use Custom Logo", AutoSize = true };
            lblLogoPath = new Label { Text = "Logo Path:", AutoSize = true };
            txtLogoPath = new TextBox { Size = new Size(250, 25), ReadOnly = true };
            btnBrowseLogo = new Button { Text = "Browse...", Size = new Size(100, 100) };

            chkUseBackgroundImage = new CheckBox { Text = "Use Background Image", AutoSize = true };
            lblBackgroundImagePath = new Label { Text = "Background Image:", AutoSize = true };
            txtBackgroundImagePath = new TextBox { Size = new Size(250, 25), ReadOnly = true };
            btnBrowseBackground = new Button { Text = "Browse...", Size = new Size(75, 25) };

            // Preview panel
            previewPanel = new Panel
            {
                Size = new Size(300, 200),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };

            // Action buttons
            btnSave = new Button
            {
                Text = "Save Configuration",
                Size = new Size(130, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSave.FlatAppearance.BorderSize = 0;

            btnCancel = new Button
            {
                Text = "Cancel",
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel
            };
            btnCancel.FlatAppearance.BorderSize = 0;

            btnReset = new Button
            {
                Text = "Reset",
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnReset.FlatAppearance.BorderSize = 0;



            // Dialogs
            colorDialog = new ColorDialog();
            openFileDialog = new OpenFileDialog
            {
                Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.ico",
                Title = "Select Image File"
            };

            // Tooltips
            toolTip = new ToolTip();
            toolTip.SetToolTip(btnPrimaryColor, "Click to select primary color");
            toolTip.SetToolTip(btnStationTextColor, "Click to select station information text color");
        }

        private void LayoutControls()
        {
            this.Controls.Add(mainPanel);
            mainPanel.Controls.AddRange(new Control[] { stationInfoGroup, appearanceGroup, imagesGroup, previewGroup });

            // Station Info Group Layout - increased height to accommodate subtitle textbox
            stationInfoGroup.SetBounds(10, 10, 300, 180);
            stationInfoGroup.Controls.AddRange(new Control[] {
                lblStationName, txtStationName, lblWelcomeMessage, txtWelcomeMessage, lblSubtitle, txtSubtitle
            });

            lblStationName.Location = new Point(15, 25);
            txtStationName.Location = new Point(15, 45);
            lblWelcomeMessage.Location = new Point(15, 75);
            txtWelcomeMessage.Location = new Point(15, 95);
            lblSubtitle.Location = new Point(15, 125);
            txtSubtitle.Location = new Point(15, 145);

            // Appearance Group Layout - improved spacing and alignment
            appearanceGroup.SetBounds(320, 10, 300, 180);
            appearanceGroup.Controls.AddRange(new Control[] {
                lblPrimaryColor, btnPrimaryColor, lblStationTextColor, btnStationTextColor
            });

            lblPrimaryColor.Location = new Point(15, 30);
            btnPrimaryColor.Location = new Point(140, 25);
            lblStationTextColor.Location = new Point(15, 70);
            btnStationTextColor.Location = new Point(140, 65);

            // Images Group Layout - moved down and increased width and height to accommodate browse buttons
            imagesGroup.SetBounds(10, 200, 650, 140);
            imagesGroup.Controls.AddRange(new Control[] {
                chkUseCustomLogo, lblLogoPath, txtLogoPath, btnBrowseLogo,
                chkUseBackgroundImage, lblBackgroundImagePath, txtBackgroundImagePath, btnBrowseBackground
            });

            chkUseCustomLogo.Location = new Point(15, 25);
            lblLogoPath.Location = new Point(15, 50);
            txtLogoPath.Location = new Point(100, 47);
            btnBrowseLogo.Location = new Point(360, 47);

            chkUseBackgroundImage.Location = new Point(15, 75);
            lblBackgroundImagePath.Location = new Point(15, 100);
            txtBackgroundImagePath.Location = new Point(130, 97);
            btnBrowseBackground.Location = new Point(390, 97);

            // Preview Group Layout - adjusted position and increased height
            previewGroup.SetBounds(670, 10, 320, 330);
            previewGroup.Controls.Add(previewPanel);
            previewPanel.Location = new Point(10, 25);

            // Action buttons - moved down to accommodate larger groups
            mainPanel.Controls.AddRange(new Control[] { btnSave, btnCancel, btnReset });
            btnSave.Location = new Point(10, 350);
            btnReset.Location = new Point(150, 350);
            btnCancel.Location = new Point(870, 350);
        }

        private void SetupEventHandlers()
        {
            // Color button events
            btnPrimaryColor.Click += (s, e) => SelectColor(btnPrimaryColor, "Primary Color");
            btnStationTextColor.Click += (s, e) => SelectColor(btnStationTextColor, "Station Text Color");

            // File browser events
            btnBrowseLogo.Click += BtnBrowseLogo_Click;
            btnBrowseBackground.Click += BtnBrowseBackground_Click;

            // Checkbox events
            chkUseCustomLogo.CheckedChanged += ChkUseCustomLogo_CheckedChanged;
            chkUseBackgroundImage.CheckedChanged += ChkUseBackgroundImage_CheckedChanged;

            // Action button events
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += (s, e) => this.Close();
            btnReset.Click += BtnReset_Click;

            // Text change events for live preview
            txtStationName.TextChanged += (s, e) => UpdatePreview();
            txtWelcomeMessage.TextChanged += (s, e) => UpdatePreview();
            txtSubtitle.TextChanged += (s, e) => UpdatePreview();
        }

        private void LoadConfiguration()
        {
            txtStationName.Text = currentConfig.StationName;
            txtWelcomeMessage.Text = currentConfig.WelcomeMessage;
            txtSubtitle.Text = currentConfig.SubtitleMessage;

            btnPrimaryColor.BackColor = currentConfig.GetPrimaryColor();
            btnStationTextColor.BackColor = currentConfig.GetStationTextColor();

            chkUseCustomLogo.Checked = currentConfig.UseCustomLogo;
            txtLogoPath.Text = currentConfig.LogoPath;

            chkUseBackgroundImage.Checked = currentConfig.UseBackgroundImage;
            txtBackgroundImagePath.Text = currentConfig.BackgroundImagePath;

            UpdateControlStates();
            UpdatePreview();
        }

        private void UpdateControlStates()
        {
            lblLogoPath.Enabled = chkUseCustomLogo.Checked;
            txtLogoPath.Enabled = chkUseCustomLogo.Checked;
            btnBrowseLogo.Enabled = chkUseCustomLogo.Checked;

            lblBackgroundImagePath.Enabled = chkUseBackgroundImage.Checked;
            txtBackgroundImagePath.Enabled = chkUseBackgroundImage.Checked;
            btnBrowseBackground.Enabled = chkUseBackgroundImage.Checked;
        }

        private void SelectColor(Button colorButton, string colorName)
        {
            colorDialog.Color = colorButton.BackColor;
            colorDialog.FullOpen = true;

            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                colorButton.BackColor = colorDialog.Color;
                UpdatePreview();
            }
        }

        private void BtnBrowseLogo_Click(object sender, EventArgs e)
        {
            openFileDialog.Title = "Select Logo Image";
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                txtLogoPath.Text = openFileDialog.FileName;
                UpdatePreview();
            }
        }

        private void BtnBrowseBackground_Click(object sender, EventArgs e)
        {
            openFileDialog.Title = "Select Background Image";
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                txtBackgroundImagePath.Text = openFileDialog.FileName;
                UpdatePreview();
            }
        }

        private void ChkUseCustomLogo_CheckedChanged(object sender, EventArgs e)
        {
            UpdateControlStates();
            UpdatePreview();
        }

        private void ChkUseBackgroundImage_CheckedChanged(object sender, EventArgs e)
        {
            UpdateControlStates();
            UpdatePreview();
        }

        private void UpdatePreview()
        {
            previewPanel.Invalidate();
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrWhiteSpace(txtStationName.Text))
                {
                    toast.ShowError("Station name is required.");
                    txtStationName.Focus();
                    return;
                }

                // Update configuration
                currentConfig.StationName = txtStationName.Text.Trim();
                currentConfig.WelcomeMessage = txtWelcomeMessage.Text.Trim();
                currentConfig.SubtitleMessage = txtSubtitle.Text.Trim();

                currentConfig.SetPrimaryColor(btnPrimaryColor.BackColor);
                currentConfig.SetStationTextColor(btnStationTextColor.BackColor);

                currentConfig.UseCustomLogo = chkUseCustomLogo.Checked;
                currentConfig.UseBackgroundImage = chkUseBackgroundImage.Checked;

                // Copy images to application directory if needed
                if (currentConfig.UseCustomLogo && !string.IsNullOrEmpty(txtLogoPath.Text))
                {
                    currentConfig.LogoPath = configService.CopyImageToAppDirectory(txtLogoPath.Text, "logo");
                }
                else
                {
                    currentConfig.LogoPath = "";
                }

                if (currentConfig.UseBackgroundImage && !string.IsNullOrEmpty(txtBackgroundImagePath.Text))
                {
                    currentConfig.BackgroundImagePath = configService.CopyImageToAppDirectory(txtBackgroundImagePath.Text, "background");
                }
                else
                {
                    currentConfig.BackgroundImagePath = "";
                }

                // Save configuration
                configService.SaveLoginConfiguration(currentConfig);

                toast.ShowSuccess("Login configuration saved successfully!");
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error saving configuration: {ex.Message}");
            }
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("Are you sure you want to reset to default configuration?",
                "Reset Configuration", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                currentConfig = new LoginConfiguration();
                LoadConfiguration();
                toast.ShowInfo("Configuration reset to defaults.");
            }
        }


    }
}
